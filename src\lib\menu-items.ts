import type { PermissionMenuitem } from '@/types/rbac';
import { Alert<PERSON>riangle, <PERSON>olderOpen, Settings, User } from 'lucide-react';

// Lift Management System menu items with their permission requirements
export const MENU_ITEMS: PermissionMenuitem[] = [
  {
    title: 'projects',
    url: '/projects',
    icon: FolderOpen,
    permission: 'projects.view',
    roles: ['contractor'],
    description: 'descriptions.projects',
  },
  {
    title: 'admin_complaint_log',
    url: '/admin/complaint-log',
    icon: AlertTriangle,
    permission: 'complaints.admin_view',
    roles: ['admin'],
    description: 'descriptions.admin_complaint_log',
  },
  {
    title: 'profile',
    url: '/profile',
    icon: User,
    permission: 'profile.view',
    roles: ['admin', 'contractor', 'viewer'],
    description: 'descriptions.profile',
  },
  {
    title: 'settings',
    url: '/settings',
    icon: Settings,
    permission: 'settings.view',
    roles: ['admin'],
    description: 'descriptions.settings',
  },
];
